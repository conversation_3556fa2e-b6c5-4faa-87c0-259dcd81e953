import React from "react";
import type { Metadata } from "next";
import Header from "@/components/comman/Header";
import Footer from "@/components/comman/Footer";
import PricingSection from "@/components/pricing/PricingSection";
import FeaturesTable from "@/components/pricing/FeaturesTable";
import FAQ from "@/components/pricing/FAQ";

export const metadata: Metadata = {
  title: "MOFSE - Pricing Plans",
  description: "Choose the perfect plan for your cryptocurrency data needs. Access comprehensive on-chain metrics, market data, and economic indicators with MOFSE's flexible pricing options.",
};

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-[#222831]">
      <Header isDarkTheme={true} />
      <main className="py-16">
        <div className="center">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Choose Your Plan
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get access to comprehensive cryptocurrency data, on-chain metrics, and economic indicators.
              Choose the plan that fits your needs.
            </p>
          </div>
          <PricingSection />

          {/* Features Comparison Table */}
          <div className="mt-20">
            <FeaturesTable />
          </div>

          {/* FAQ Section */}
          <div className="mt-20">
            <FAQ />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
