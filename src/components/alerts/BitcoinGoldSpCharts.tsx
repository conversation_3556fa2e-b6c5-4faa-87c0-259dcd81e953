import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../home/<USER>'
import FullscreenWrapper from '../comman/FullscreenWrapper'
import { FullscreenProvider } from '../comman/FullscreenProvider'
import FullscreenButton from '../comman/FullscreenButton'
import DataDownloadButton from '../comman/DataDownloadButton'
import APIDocsButton from '../comman/APIDocsButton'

export default function BitcoinGoldSpCharts() {
  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center w-full">
        <div className="flex gap-2">
          <DataDownloadButton />
          <APIDocsButton />
        </div>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="Bitcoin vs Gold vs S&P 500">
        <div className="w-full space-y-8 py-6">
          <HeroSectionChart/>
        </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  )
}