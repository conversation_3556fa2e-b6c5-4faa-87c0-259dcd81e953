"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import { formatChartDate } from "@/lib/utils";
import Image from "next/image";
import { formatTooltip } from "../mofse-advance/utils";
import { Coin } from "@/lib/api.interface";
import { formatYAxisTick } from "./utils";

// Default chart colors - Bitcoin specific
const DEFAULT_COLORS = {
  BORDER: "#409E13",
  DARK: "#62A442",
  FILL: "#CAF2B6",
  WHITE: "#FFFFFF",
};

// Function to get coin-specific colors
function getCoinColors(coin?: string) {
  switch (coin?.toUpperCase()) {
    case "BTC":
    case "BITCOIN":
      return {
        FILL: "#f7931a",
        BORDER: "#e6830f",
      };
    case "ETH":
    case "ETHEREUM":
      return {
        FILL: "#8A8BB5",
        BORDER: "#A1A9D8",
      };
    case "USDT":
      return {
        FILL: "#26A17B",
        BORDER: "#1e8a66",
      };
    case "USDC":
      return {
        FILL: "#2775CA",
        BORDER: "#1f5fa3",
      };
    case "BNB": 
      return {
        FILL: "#f1b90a",
        BORDER: "#ffce36",
      };
    case "SOL":
      return {
        FILL: "#9945FF",
        BORDER: "#7a37cc",
      };
    default:
      return {
        FILL: DEFAULT_COLORS.FILL,
        BORDER: DEFAULT_COLORS.BORDER,
      };
  }
}

// Data point type for the chart
export type DataPoint = {
  date: string;
  count: number;
  timestamp: number;
};

export type Options = {
  showDay?: boolean;
  showMonth?: boolean;
  showYear?: boolean;
};

type ChartPropTypes = {
  data: DataPoint[];
  coin?: Coin | null;
  chartType?: "bitcoin" | "other";
  options?: Options;
  onChainFilter?: string;
};

const TransactionCountHistoryBarGraph = ({
  data,
  coin,
  options,
  onChainFilter = 'transaction-count'
}: ChartPropTypes) => {
  const colors = getCoinColors(coin?.symbol);

  return (
    <div style={{ width: "100%", height: 500 , position: "relative", color: "black"}}>
      <Image
        src="/MOFSE.svg"
        width={140}
        height={40}
        alt="Watermark"
        style={{
          zIndex: '1',
          position: "absolute",
          top: "20px", 
          right: "110px",
          opacity: 0.6,
          pointerEvents: "none", 
          filter: "grayscale(100%)"
        }}
      />
      <ResponsiveContainer width="100%" height={500}>
        <BarChart
          data={data}
          // margin={{ top: 5, right: 0, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="6 6" stroke="#5d5e5f" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 10, fill: 'white'}}
            tickFormatter={(value) => formatChartDate(value, options)}
            tickLine={false}
            axisLine={false}
            minTickGap={40}
          />
          <YAxis
            orientation="right"
            domain={[0, "dataMax"]}
            tickFormatter={formatYAxisTick}
            tick={{ fontSize: 10, fill: 'white' }}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            formatter={(value) => formatTooltip(value as string, onChainFilter)}
          />
          <Bar
            dataKey="count"
            fill={colors.FILL}
            stroke={colors.BORDER}
            strokeWidth={1}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TransactionCountHistoryBarGraph;
