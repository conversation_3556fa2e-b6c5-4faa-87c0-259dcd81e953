"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Bar<PERSON><PERSON>,
} from "recharts";
import ChartOverlay from "../comman/ChartOverlay";
import FullscreenWrapper from "../comman/FullscreenWrapper";
import { FullscreenProvider } from "../comman/FullscreenProvider";
import FullscreenButton from "../comman/FullscreenButton";
import DataDownloadButton from "../comman/DataDownloadButton";
import APIDocsButton from "../comman/APIDocsButton";

// Static data for China e-CNY transaction volume
const chinaData = [
   {
    year: "2021",
    volume: 0.09, // 0.09 Trillion
    displayValue: "¥0.09T",
    fullValue: 90000000000, // 0.09 trillion in actual value
  },
  {
    year: "2022",
    volume: 0.1, // 0.1 Trillion
    displayValue: "¥0.1T",
    fullValue: 100000000000, // 0.1 trillion in actual value
  },
  {
    year: "2023", 
    volume: 1.8, // 1.8 Trillion
    displayValue: "¥1.8T",
    fullValue: 1800000000000, // 1.8 trillion in actual value
  },
  {
    year: "2024",
    volume: 7.0, // 7 Trillion
    displayValue: "¥7T",
    fullValue: 7000000000000, // 7 trillion in actual value
  },
];

const ChinaECNYChart = () => {
  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center w-full">
        <div className="flex gap-2">
          <DataDownloadButton />
          <APIDocsButton />
        </div>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="China e-CNY Transaction Volume">
        <div className="w-full rounded-lg shadow-sm">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-white mb-2">
          e-CNY transaction volume quadruples from 2023
        </h3>
        <p className="text-sm text-white mb-1">
          Cumulative transaction volumes for select months (2021-2024)
        </p>
        {/* <p className="text-xs text-blue-600">
          Source: People&apos;s Bank of China
        </p> */}
      </div>
      
      <ChartOverlay overlayClassName="justify-center">
        <div style={{ width: "100%", height: 500, color: 'white' }}>
          <ResponsiveContainer width="100%" height={500}>
            <BarChart
              data={chinaData}
              barCategoryGap="30%"
            >
              <CartesianGrid strokeDasharray="6 6" stroke="#5d5e5f" />
              <XAxis
                dataKey="year"
                tick={{ fontSize: 10, fill: "#fff" }}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                domain={[0, 8]}
                orientation="right"
                tick={{ fontSize: 10, fill: "#fff" }}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `¥${value}T`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#ffffff",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                  color: 'black'
                }}
                formatter={(value) => [
                  chinaData.find(d => d.volume === value)?.displayValue || `¥${value}T`,
                  "Transaction Volume"
                ]}
                labelFormatter={(label) => `Year: ${label}`}
              />
              <Bar
                dataKey="volume"
                fill="#f87171"
                radius={[4, 4, 0, 0]}
                stroke="#ef4444"
                strokeWidth={1}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </ChartOverlay>
      </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  );
};

export default ChinaECNYChart;
