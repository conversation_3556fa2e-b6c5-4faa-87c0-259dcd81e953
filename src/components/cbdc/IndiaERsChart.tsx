"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import ChartOverlay from "../comman/ChartOverlay";
import FullscreenWrapper from "../comman/FullscreenWrapper";
import { FullscreenProvider } from "../comman/FullscreenProvider";
import FullscreenButton from "../comman/FullscreenButton";
import DataDownloadButton from "../comman/DataDownloadButton";
import APIDocsButton from "../comman/APIDocsButton";

// Static data for India eRs circulation
const indiaData = [
  {
    year: "2023",
    circulation: 1.64, // 1.64 million
    displayValue: "₹1.64M",
    fullValue: 1640000,
  },
  {
    year: "2024", 
    circulation: 23.41, // 23.41 million
    displayValue: "₹23.41M",
    fullValue: 23410000,
  },
  {
    year: "2025",
    circulation: 101.65, // 101.6 million 
    displayValue: "₹101.65M",
    fullValue: 101650000,
  },
];

const IndiaERsChart = () => {
  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center w-full">
        <div className="flex gap-2">
          <DataDownloadButton />
          <APIDocsButton />
        </div>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="India e-Rupee Circulation">
        <div className="w-full  rounded-lg shadow-sm">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-white mb-2">
          Total e-rupee in circulation
        </h3>
        <p className="text-sm text-white mb-1">
          From 2023-March 2025 (in millions)
        </p>
        {/* <p className="text-xs text-blue-600">
          Source: Reserve Bank of India
        </p> */}
      </div>
      
      <ChartOverlay overlayClassName="justify-center">
        <div style={{ width: "100%", height: 500, color: 'white' }}>
          <ResponsiveContainer width="100%" height={500}>
            <BarChart
              data={indiaData}
              barCategoryGap="30%"
            >
              <CartesianGrid strokeDasharray="6 6" stroke="#5d5e5f" />
              <XAxis
                dataKey="year"
                tick={{ fontSize: 10, fill: "#fff" }}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                orientation="right"
                domain={[0, 110]}
                tick={{ fontSize: 10, fill: "#fff" }}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `₹${value}M`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#ffffff",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                  color: 'black'
                }}
                formatter={(value) => [
                  indiaData.find(d => d.circulation === value)?.displayValue || `₹${value}M`,
                  "Circulation"
                ]}
                labelFormatter={(label) => `Year: ${label}`}
              />
              <Bar
                dataKey="circulation"
                fill="#06b6d4"
                radius={[4, 4, 0, 0]}
                stroke="#0891b2"
                strokeWidth={1}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </ChartOverlay>
      </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  );
};

export default IndiaERsChart;
