"use client";

import React, { useState } from "react";
import { Download } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";

interface DataDownloadButtonProps {
  className?: string;
}

const DataDownloadButton: React.FC<DataDownloadButtonProps> = ({
  className,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <button
        onClick={handleClick}
        title="Download Data"
        className={
          className ||
          "flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gray-800/80 hover:bg-gray-700/80 rounded-sm transition-colors border border-gray-500 cursor-pointer"
        }
      >
        <span>Download</span>
        <Download size={16} />
      </button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-600">
          <DialogHeader>
            <DialogTitle>Data Download</DialogTitle>
            <DialogDescription className="text-gray-300">
              This feature is currently under development!
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DataDownloadButton;
