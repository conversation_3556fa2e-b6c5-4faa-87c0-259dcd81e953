"use client";

import React from "react";
import { Maximize2, Minimize2 } from "lucide-react";
import { useFullscreen } from "./FullscreenProvider"; // Import the custom hook

interface FullscreenButtonProps {
  className?: string;
}

const FullscreenButton: React.FC<FullscreenButtonProps> = ({
  className,
}) => {
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  return (
    <button
      onClick={toggleFullscreen}
      title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
      className={
        className ||
        "flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gray-800/80 hover:bg-gray-700/80 rounded-sm transition-colors border border-gray-500 cursor-pointer"
      }
    >
      {isFullscreen ? (
        <>
          <span>Exit Fullscreen</span>
          <Minimize2 size={16} />
        </>
      ) : (
        <>
          <span>Fullscreen</span>
          <Maximize2 size={16} />
        </>
      )}
    </button>
  );
};

export default FullscreenButton;