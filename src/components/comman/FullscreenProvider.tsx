"use client";

import React, {
  useState,
  useRef,
  useEffect,
  createContext,
  useContext,
  useCallback,
  ReactNode,
  RefObject,
} from "react";

// 1. Define the Context's shape (Corrected line)
interface FullscreenContextType {
  isFullscreen: boolean;
  // Change: Allow the ref's generic type to be potentially null
  containerRef: RefObject<HTMLDivElement | null>; 
  toggleFullscreen: () => void;
}

// 2. Create the Context
const FullscreenContext = createContext<FullscreenContextType | null>(null);

// 3. Create the Provider Component
interface FullscreenProviderProps {
  children: ReactNode;
}

export const FullscreenProvider: React.FC<FullscreenProviderProps> = ({
  children,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  // Change: Match the ref's type to the interface
  const containerRef = useRef<HTMLDivElement | null>(null); 

  const toggleFullscreen = useCallback(async () => {
    // The rest of the logic remains the same
    const element = containerRef.current;
    if (!element) return;

    if (!document.fullscreenElement) {
      try {
        await element.requestFullscreen();
      } catch (error) {
        console.error("Error entering fullscreen:", error);
      }
    } else {
      try {
        await document.exitFullscreen();
      } catch (error) {
        console.error("Error exiting fullscreen:", error);
      }
    }
  }, []);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  return (
    <FullscreenContext.Provider
      value={{ isFullscreen, containerRef, toggleFullscreen }}
    >
      {children}
    </FullscreenContext.Provider>
  );
};

// 4. Create a custom hook for easy access to the context
export const useFullscreen = () => {
  const context = useContext(FullscreenContext);
  if (!context) {
    throw new Error("useFullscreen must be used within a FullscreenProvider");
  }
  return context;
};