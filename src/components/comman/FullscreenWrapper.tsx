"use client";

import React from "react";
import { Minimize2 } from "lucide-react";
import { useFullscreen } from "./FullscreenProvider"; // Import the custom hook

interface FullscreenWrapperProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

const FullscreenWrapper: React.FC<FullscreenWrapperProps> = ({
  children,
  title = "Chart",
  className = "",
}) => {
  const { isFullscreen, containerRef, toggleFullscreen } = useFullscreen();

  return (
    // This div becomes the target for the fullscreen API call
    <div
      ref={containerRef}
      className={!isFullscreen ? className : ""} // Apply user's class only in normal mode
      style={
        isFullscreen
          ? {
              width: "100%",
              height: "100%",
              backgroundColor: "#222831",
              padding: "0.5rem",
              display: "flex",
              flexDirection: "column",
            }
          : {}
      }
    >
      {isFullscreen ? (
        <>
          {/* == Fullscreen Header == */}
          <div className="flex items-center justify-between pb-2 pt-1 shrink-0">
            <h2 className="text-xl text-white">{title}</h2>
            {/* Essential exit button for usability inside fullscreen mode */}
            <button
              onClick={toggleFullscreen}
              className="p-2 bg-gray-700/80 hover:bg-gray-600/80 text-white rounded-md cursor-pointer"
              title="Exit fullscreen"
            >
              <Minimize2 size={18} />
            </button>
          </div>
          {/* == Fullscreen Content == */}
          <div className="flex-1 min-h-0 overflow-auto">{children}</div>
        </>
      ) : (
        // In normal mode, just render the children
        children
      )}
    </div>
  );
};

export default FullscreenWrapper;