"use client";

import { useState } from "react";
import BTCvsGOLDvsSP500, { DataPoint } from "./BTCvsGOLDvsSP500";
import { cn } from "@/lib/utils";
import {  useFinanceHistory } from "@/lib/state";
import { FinanceHistoryData } from "@/lib/api.interface";
import Loader from "../comman/Loader";
// import AssetPriceCard from "./AssetPriceCard";


const frequencyOptions = [
  {
    value: "last30",
    label: "1M",
  },
  {
    value: "last180",
    label: "6M",
  },
  {
    value: "lastYear",
    label: "1Y",
  },
  {
    value: "last3year",
    label: "3Y",
  },
  {
    value: "last5year",
    label: "5Y",
  },
];

export function transformChartData(data: FinanceHistoryData): DataPoint[] {
  if (!data) {
    return [];
  }

  const bitcoinMap = new Map(
    data.bitcoin.map((d) => [d.date.split("T")[0], d.adjClose])
  );
  const sp500Map = new Map(
    data.sp500.map((d) => [d.date.split("T")[0], d.adjClose])
  );
  const goldMap = new Map(
    data.gold.map((d) => [d.date.split("T")[0], d.adjClose])
  );

  // Collect all unique dates across all three sources
  const dateSet = new Set<string>([
    ...bitcoinMap.keys(),
    ...sp500Map.keys(),
    ...goldMap.keys(),
  ]);

  const allDates = Array.from(dateSet).sort();

  const mergedData: DataPoint[] = allDates.reduce<DataPoint[]>((acc, date) => {
    const bitcoin = bitcoinMap.get(date);
    const sp500 = sp500Map.get(date);
    const gold = goldMap.get(date);

    // Only include this date if all three values are present
    if (bitcoin != null && sp500 != null && gold != null) {
      acc.push({
        time: date,
        bitcoin,
        sp500,
        gold,
      });
    }

    return acc;
  }, []);

  return mergedData;
}

export function HeroSectionChart() {
  const [active, setActive] = useState("last5year");
  const financeHistoryQuery = useFinanceHistory(active);
  const financeHistory = transformChartData(financeHistoryQuery.data!);

  // const lastBitcoin = financeHistory[financeHistory.length - 1]?.bitcoin;
  // const lastGold = financeHistory[financeHistory.length - 1]?.gold;
  // const lastSP500 = financeHistory[financeHistory.length - 1]?.sp500;

  const isLastMonth = active === "last30";

  // const coinsQuery = useCoins({
  //   orderBy: "marketCap",
  //   offset: 0,
  //   limit: 20,
  //   order: "DESC",
  //   searchText: "",
  //   isStableCoinsEnabled: false,
  // });
  // const coins = coinsQuery.data?.data.coins || [];
  // const bitcoin = coins[0];

  const handleClick = (value: string) => {
    setActive(value);
  };

  return (
    <div className="flex gap-4 flex-col sm:flex-row text-black">

      {/* <div className="flex flex-col justify-between gap-4 sm:gap-0">
        <AssetPriceCard
          color={COLORS.BITCOIN}
          name="BTC"
          price={`$${formatPrice(parseFloat(bitcoin?.price || "0"))}`}
          updatedAt={timeAgo(coinsQuery.dataUpdatedAt)}
        />
        <AssetPriceCard
          color={COLORS.GOLD}
          name="Gold"
          price={`$${formatPrice(lastGold)}`}
          updatedAt={timeAgo(financeHistoryQuery.dataUpdatedAt)}
        />
        <AssetPriceCard
          color={COLORS.SP500}
          name="S&P 500"
          price={formatPrice(lastSP500)}
          updatedAt={timeAgo(financeHistoryQuery.dataUpdatedAt)}
        />
      </div> */}

      
        <div className="h-auto w-full ">
          <div className="flex justify-end gap-2 md:gap-4 mb-8">
            {frequencyOptions.map(({ value, label }) => (
              <span
                className={cn(
                  "px-4 py-2 text-sm rounded border border-border-light cursor-pointer text-white",
                  active === value ? "bg-bg-primary" : ""
                )}
                key={value}
                onClick={() => handleClick(value)}
              >
                {label}
              </span>
            ))}
          </div>
          {/* <div className="flex text-white mb-4 justify-end">
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <span
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: COLORS.BITCOIN }}
                ></span>
                <span className="text-base font-semibold">Bitcoin</span>
              </div>
              <div className="flex items-center gap-2">
                <span
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: COLORS.GOLD }}
                ></span>
                <span className="text-base font-semibold">Gold</span>
              </div>
              <div className="flex items-center gap-2">
                <span
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: COLORS.SP500 }}
                ></span>
                <span className="text-base font-semibold">S&P 500</span>
              </div>
            </div>
          </div> */}
          {financeHistoryQuery.isLoading ? (
            <Loader />
          ) : (
            <BTCvsGOLDvsSP500 data={financeHistory} isLastMonth={isLastMonth}/>
          )}
        </div>

    </div>
  );
}
