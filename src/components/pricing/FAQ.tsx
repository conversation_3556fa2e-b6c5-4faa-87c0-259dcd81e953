"use client";

import React from "react";
import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQ = () => {
  const faqs = [
    {
      question: "What's the difference between Free and Standard versions?",
      answer: "The Free version provides 1 month of data for basic on-chain metrics and economic indicators. The Standard version includes all historical data, cryptocurrency market data, stablecoins metrics, CBDC tracking, and API access."
    },
    {
      question: "How often is the data updated?",
      answer: "Our data is updated in real-time for most metrics. On-chain data is typically updated within minutes of new blocks being mined, while market data is updated continuously throughout trading hours."
    },
    {
      question: "Do you provide API access?",
      answer: "Yes, API access is included with the Standard version. The Free version has limited API access. Enterprise customers can get dedicated API endpoints and higher rate limits."
    },
    {
      question: "What cryptocurrencies do you support?",
      answer: "We support Bitcoin (BTC), Ethereum (ETH), Binance Coin (BNB), Solana (SOL), and major stablecoins like USDT and USDC. Additional cryptocurrencies are available in the Standard version."
    },
    {
      question: "Can I upgrade or downgrade my plan?",
      answer: "Yes, you can upgrade from Free to Standard at any time by contacting our sales team. For enterprise solutions, we'll work with you to customize a plan that fits your needs."
    },
    {
      question: "What kind of support do you provide?",
      answer: "Free users get community support through our documentation. Standard users receive priority email support. Enterprise customers get dedicated support with guaranteed response times."
    },
    {
      question: "Do you offer custom integrations?",
      answer: "Yes, we offer custom integrations and enterprise solutions. Contact our enterprise sales <NAME_EMAIL> to discuss your specific requirements."
    },
    {
      question: "Is there a trial period for the Standard version?",
      answer: "Please contact our sales team to discuss trial options for the Standard version. We're happy to provide demonstrations and discuss your specific use case."
    }
  ];

  return (
    <div className="bg-[#2d3748] rounded-lg border border-[#404040] p-8">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-white mb-4">
          Frequently Asked Questions
        </h3>
        <p className="text-gray-300">
          Have questions about our pricing plans? Find answers below.
        </p>
      </div>
      
      <Accordion type="single" collapsible className="space-y-4">
        {faqs.map((faq, index) => (
          <AccordionItem 
            key={index} 
            value={`item-${index}`}
            className="border border-[#404040] rounded-lg bg-[#34404f] px-6"
          >
            <AccordionTrigger className="text-white hover:text-[#bbd955] text-left">
              {faq.question}
            </AccordionTrigger>
            <AccordionContent className="text-gray-300 pt-2">
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
      
      <div className="text-center mt-8 pt-8 border-t border-[#404040]">
        <p className="text-gray-300 mb-4">
          Still have questions? We're here to help.
        </p>
        <button
          onClick={() => {
            window.location.href = "mailto:<EMAIL>?subject=MOFSE Pricing Questions";
          }}
          className="text-[#bbd955] hover:text-[#a5c449] font-semibold underline"
        >
          Contact our sales team
        </button>
      </div>
    </div>
  );
};

export default FAQ;
