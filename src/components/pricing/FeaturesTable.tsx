"use client";

import React from "react";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";

const FeaturesTable = () => {
  const features = [
    {
      category: "On-Chain Metrics",
      items: [
        { name: "Transaction Volume", free: true, standard: true },
        { name: "Transaction Count", free: true, standard: true },
        { name: "Unique Sending Wallets", free: true, standard: true },
        { name: "Unique Receiving Wallets", free: true, standard: true },
        { name: "Total Unique Wallets", free: true, standard: true },
        { name: "New Wallets Created", free: true, standard: true },
        { name: "Block Mined", free: true, standard: true },
        { name: "Average Transaction Value", free: true, standard: true },
        { name: "Total Fee", free: true, standard: true },
        { name: "Wallets Sending > 1 BTC", free: true, standard: true },
        { name: "Wallets Sending > 10 BTC", free: true, standard: true },
        { name: "Wallets Sending > 100 BTC", free: true, standard: true },
      ]
    },
    {
      category: "Coins & Market Cap",
      items: [
        { name: "Cryptocurrencies", free: false, standard: true },
        { name: "Top Gainers", free: false, standard: true },
        { name: "Top Losers", free: false, standard: true },
      ]
    },
    {
      category: "Economic Indicators",
      items: [
        { name: "BTC vs Gold vs S&P 500", free: true, standard: true },
        { name: "M2 vs BTC Price", free: true, standard: true },
        { name: "Consumer Index vs BTC", free: true, standard: true },
        { name: "10 Year Treasury", free: true, standard: true },
        { name: "Retail Sales", free: true, standard: true },
        { name: "Federal Debt", free: true, standard: true },
        { name: "Unemployment Rate", free: true, standard: true },
        { name: "Federal Funds Rate", free: true, standard: true },
      ]
    },
    {
      category: "ETFs, Funding Rates & Exchanges",
      items: [
        { name: "Bitcoin ETF data", free: "TBD", standard: "TBD" },
        { name: "Ethereum ETF data", free: "TBD", standard: "TBD" },
        { name: "Funding rates data", free: "TBD", standard: "TBD" },
      ]
    },
    {
      category: "Stablecoins",
      items: [
        { name: "On-Chain Metrics", free: false, standard: true },
        { name: "Sending Address", free: false, standard: true },
        { name: "Receiving Address", free: false, standard: true },
        { name: "Active Address", free: false, standard: true },
        { name: "Transaction Count", free: false, standard: true },
        { name: "Transaction Volume", free: false, standard: true },
        { name: "Coins & Market Cap", free: false, standard: true },
        { name: "Coin Ranking", free: false, standard: true },
      ]
    },
    {
      category: "CBDC Tracker",
      items: [
        { name: "CBDC Tracker", free: false, standard: true },
        { name: "Circulation", free: false, standard: true },
        { name: "China", free: false, standard: true },
        { name: "India", free: false, standard: true },
      ]
    }
  ];

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === "string") {
      return (
        <span className="text-yellow-400 text-sm font-medium">
          {value}
        </span>
      );
    }
    return value ? (
      <Check className="h-5 w-5 text-[#bbd955] mx-auto" />
    ) : (
      <X className="h-5 w-5 text-red-400 mx-auto" />
    );
  };

  return (
    <div className="bg-[#2d3748] rounded-lg border border-[#404040] overflow-hidden">
      <div className="p-6 border-b border-[#404040]">
        <h3 className="text-2xl font-bold text-white text-center">
          Detailed Features Comparison
        </h3>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-[#404040]">
              <th className="text-left p-4 text-white font-semibold">Features</th>
              <th className="text-center p-4 text-white font-semibold">Free Version</th>
              <th className="text-center p-4 text-white font-semibold">Standard Version</th>
            </tr>
          </thead>
          <tbody>
            {features.map((category, categoryIndex) => (
              <React.Fragment key={categoryIndex}>
                <tr className="bg-[#1a1a1a]">
                  <td colSpan={3} className="p-4 text-[#bbd955] font-semibold text-lg">
                    {category.category}
                  </td>
                </tr>
                {category.items.map((item, itemIndex) => (
                  <tr 
                    key={itemIndex} 
                    className={cn(
                      "border-b border-[#404040] hover:bg-[#3a4553] transition-colors",
                      itemIndex % 2 === 0 ? "bg-[#2d3748]" : "bg-[#34404f]"
                    )}
                  >
                    <td className="p-4 text-white">{item.name}</td>
                    <td className="p-4 text-center">
                      {renderFeatureValue(item.free)}
                    </td>
                    <td className="p-4 text-center">
                      {renderFeatureValue(item.standard)}
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default FeaturesTable;
