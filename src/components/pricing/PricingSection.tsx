"use client";

import React from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import Button from "@/components/comman/Button";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";

const PricingSection = () => {
  const plans = [
    {
      name: "Free Version",
      subtitle: "1 Month Data",
      price: "Free",
      description: "Perfect for getting started with basic cryptocurrency data",
      features: [
        { name: "Transaction Volume", included: true },
        { name: "Transaction Count", included: true },
        { name: "Unique Sending Wallets", included: true },
        { name: "Unique Receiving Wallets", included: true },
        { name: "Total Unique Wallets", included: true },
        { name: "New Wallets Created", included: true },
        { name: "Block Mined", included: true },
        { name: "Average Transaction Value", included: true },
        { name: "Total Fee", included: true },
        { name: "Wallets Sending > 1 BTC", included: true },
        { name: "Wallets Sending > 10 BTC", included: true },
        { name: "Wallets Sending > 100 BTC", included: true },
        { name: "BTC vs Gold vs S&P 500", included: true },
        { name: "M2 vs BTC Price", included: true },
        { name: "Consumer Index vs BTC", included: true },
        { name: "10 Year Treasury", included: true },
        { name: "Retail Sales", included: true },
        { name: "Federal Debt", included: true },
        { name: "Unemployment Rate", included: true },
        { name: "Federal Funds Rate", included: true },
        { name: "Cryptocurrencies", included: false },
        { name: "Top Gainers", included: false },
        { name: "Top Losers", included: false },
        { name: "Stablecoins On-Chain Metrics", included: false },
        { name: "CBDC Tracker", included: false },
      ],
      popular: false,
      buttonText: "Get Started",
      buttonVariant: "outline" as const,
    },
    {
      name: "Standard Version",
      subtitle: "All Data",
      price: "Contact Us",
      description: "Complete access to all cryptocurrency data and analytics",
      features: [
        { name: "All Free Version Features", included: true },
        { name: "Cryptocurrencies", included: true },
        { name: "Top Gainers", included: true },
        { name: "Top Losers", included: true },
        { name: "Stablecoins On-Chain Metrics", included: true },
        { name: "Sending Address", included: true },
        { name: "Receiving Address", included: true },
        { name: "Active Address", included: true },
        { name: "Stablecoins Transaction Count", included: true },
        { name: "Stablecoins Transaction Volume", included: true },
        { name: "Coins & Market Cap", included: true },
        { name: "Coin Ranking", included: true },
        { name: "CBDC Tracker", included: true },
        { name: "CBDC Circulation", included: true },
        { name: "China CBDC Data", included: true },
        { name: "India CBDC Data", included: true },
        { name: "Historical Data Access", included: true },
        { name: "API Access", included: true },
        { name: "Priority Support", included: true },
      ],
      popular: true,
      buttonText: "Contact Sales",
      buttonVariant: "default" as const,
    },
  ];

  return (
    <div className="space-y-16">
      {/* Pricing Cards */}
      <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <Card
            key={index}
            className={cn(
              "relative bg-[#2d3748] border-[#404040] text-white",
              plan.popular && "border-[#bbd955] shadow-lg shadow-[#bbd955]/20"
            )}
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-[#bbd955] text-black px-4 py-2 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>
            )}

            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl font-bold text-white">
                {plan.name}
              </CardTitle>
              <CardDescription className="text-[#bbd955] font-medium text-lg">
                {plan.subtitle}
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-white">
                  {plan.price}
                </span>
              </div>
              <p className="text-gray-300 mt-2">
                {plan.description}
              </p>
            </CardHeader>

            <CardContent className="space-y-3">
              <div className="max-h-96 overflow-y-auto pr-2">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center gap-3">
                    {feature.included ? (
                      <Check className="h-5 w-5 text-[#bbd955] flex-shrink-0" />
                    ) : (
                      <X className="h-5 w-5 text-red-400 flex-shrink-0" />
                    )}
                    <span className={cn(
                      "text-sm",
                      feature.included ? "text-white" : "text-gray-400"
                    )}>
                      {feature.name}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>

            <CardFooter className="pt-6">
              <Button
                variant={plan.buttonVariant}
                className={cn(
                  "w-full py-3 font-semibold",
                  plan.buttonVariant === "default"
                    ? "bg-[#bbd955] hover:bg-[#a5c449] text-black"
                    : "border-[#bbd955] text-[#bbd955] hover:bg-[#bbd955] hover:text-black"
                )}
                onClick={() => {
                  if (plan.name === "Standard Version") {
                    window.location.href = "mailto:<EMAIL>?subject=MOFSE Standard Version Inquiry";
                  } else {
                    // Handle free version signup
                    window.location.href = "/sign-up";
                  }
                }}
              >
                {plan.buttonText}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Enterprise Contact Section */}
      <div className="text-center bg-[#2d3748] rounded-lg p-8 border border-[#404040]">
        <h3 className="text-2xl font-bold text-white mb-4">
          Need Enterprise Solutions?
        </h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Looking for custom integrations, dedicated support, or enterprise-level data access?
          Contact our team to discuss your specific requirements.
        </p>
        <Button
          className="bg-[#bbd955] hover:bg-[#a5c449] text-black font-semibold px-8 py-3"
          onClick={() => {
            window.location.href = "mailto:<EMAIL>?subject=MOFSE Enterprise Solutions Inquiry";
          }}
        >
          Contact Enterprise Sales
        </Button>
      </div>
    </div>
  );
};

export default PricingSection;
